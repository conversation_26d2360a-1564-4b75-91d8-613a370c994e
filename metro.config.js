// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('@expo/metro-config');

const config = getDefaultConfig(__dirname);

// Resolve JSON parsing issues
config.transformer = {
  ...config.transformer,
  minifierConfig: {
    ...config.transformer.minifierConfig,
    keep_classnames: true,
    keep_fnames: true,
    mangle: {
      ...config.transformer.minifierConfig?.mangle,
      keep_classnames: true,
      keep_fnames: true
    },
    output: {
      ...config.transformer.minifierConfig?.output,
      ascii_only: true
    }
  }
};

// Add support for additional file extensions and platform-specific extensions
config.resolver.sourceExts = [
  ...config.resolver.sourceExts,
  'mjs',
  'cjs',
  'web.js',
  'web.ts',
  'web.tsx'
];

// Ensure platform-specific extensions are properly handled
config.resolver.platforms = ['web', 'ios', 'android'];

module.exports = config;
