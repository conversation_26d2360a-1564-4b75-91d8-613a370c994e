import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
} from 'react-native';
import { router } from 'expo-router';
import { useTheme } from '../../../context/theme';
import { useAuth } from '../../../context/auth';
import { supabase } from '../../../lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';

type Appointment = {
  id: string;
  professional: {
    id: string;
    type: 'doctor' | 'lawyer';
    speciality: string;
    user: {
      first_name: string;
      last_name: string;
    };
  };
  date_time: string;
  status: string;
  type: string;
  notes: string | null;
};

export default function AppointmentHistoryScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadAppointmentHistory();
  }, []);

  const loadAppointmentHistory = async () => {
    try {
      setLoading(true);
      setError(null);

      // Récupérer les rendez-vous sans utiliser de relations
      const { data: appointmentsData, error: appointmentsError } = await supabase
        .from('appointments')
        .select('id, date_time, status, type, notes, professional_id')
        .eq('user_id', user?.id)
        .in('status', ['completed', 'cancelled'])
        .order('date_time', { ascending: false });

      if (appointmentsError) throw appointmentsError;

      if (!appointmentsData || appointmentsData.length === 0) {
        setAppointments([]);
        setLoading(false);
        return;
      }

      // Créer des rendez-vous avec des informations minimales
      const initialAppointments = appointmentsData.map(item => ({
        id: item.id,
        date_time: item.date_time,
        status: item.status,
        type: item.type,
        notes: item.notes,
        professional: {
          id: item.professional_id,
          type: 'doctor' as 'doctor' | 'lawyer',
          speciality: 'Non spécifié',
          user: {
            first_name: 'Professionnel',
            last_name: 'de santé'
          }
        }
      })) as Appointment[];

      setAppointments(initialAppointments);

      // Récupérer les détails des professionnels en arrière-plan
      loadProfessionalDetails(appointmentsData, initialAppointments);
    } catch (err) {
      console.error('Error loading appointment history:', err);
      setError('Erreur lors du chargement de l\'historique');
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour charger les détails des professionnels
  const loadProfessionalDetails = async (appointmentsData: any[], initialAppointments: Appointment[]) => {
    try {
      const professionalIds = appointmentsData.map(item => item.professional_id);
      
      // Requête simple pour récupérer les professionnels sans utiliser de relations
      const { data: professionalsData, error: professionalsError } = await supabase
        .from('professionals')
        .select('id, type, speciality, user_id')
        .in('id', professionalIds);

      if (professionalsError || !professionalsData) {
        console.error('Error fetching professionals:', professionalsError);
        return; // Garder les données minimales
      }

      // Récupérer les user_ids des professionnels
      const userIds = professionalsData
        .map(prof => prof.user_id)
        .filter(id => id !== null && id !== undefined);

      // Si nous avons des user_ids, récupérer les informations des utilisateurs depuis profiles
      let profilesData: any[] = [];
      if (userIds.length > 0) {
        const { data: profiles, error: profilesError } = await supabase
          .from('profiles')
          .select('id, first_name, last_name')
          .in('id', userIds);

        if (!profilesError && profiles) {
          profilesData = profiles;
        } else {
          console.error('Error fetching profiles:', profilesError);
        }
      }

      // Créer un dictionnaire pour un accès rapide aux données des profils
      const profilesById: Record<string, any> = {};
      profilesData.forEach(profile => {
        if (profile.id) {
          profilesById[profile.id] = profile;
        }
      });

      // Mettre à jour les rendez-vous avec les informations des professionnels et des profils
      const updatedAppointments = initialAppointments.map(appointment => {
        // Trouver le professionnel correspondant
        const professional = professionalsData.find(p => p.id === appointment.professional.id);
        if (!professional) return appointment;
        
        // S'assurer que toutes les valeurs sont des chaînes de caractères
        const professionalType = typeof professional.type === 'string' 
          ? professional.type as 'doctor' | 'lawyer' 
          : 'doctor';
          
        const speciality = typeof professional.speciality === 'string' 
          ? professional.speciality 
          : 'Non spécifié';

        // Récupérer les informations du profil si disponibles
        let firstName = 'Professionnel';
        let lastName = 'de santé';
        
        if (professional.user_id && profilesById[professional.user_id]) {
          const profile = profilesById[professional.user_id];
          firstName = profile.first_name || firstName;
          lastName = profile.last_name || lastName;
        }
        
        // Mettre à jour les informations du professionnel
        return {
          ...appointment,
          professional: {
            ...appointment.professional,
            type: professionalType,
            speciality: speciality,
            user: {
              first_name: firstName,
              last_name: lastName
            }
          }
        };
      });

      setAppointments(updatedAppointments);
    } catch (error) {
      console.error('Error loading professional details:', error);
      // Ne pas échouer, garder les données minimales
    }
  };

  const formatAppointmentDate = (date: string) => {
    return format(new Date(date), 'PPP à HH:mm');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return theme.colors.primary;
      case 'cancelled':
        return theme.colors.error;
      default:
        return theme.colors.gray[500];
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Terminé';
      case 'cancelled':
        return 'Annulé';
      default:
        return status;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}>
          <Ionicons
            name="arrow-back"
            size={24}
            color={theme.colors.text}
          />
        </TouchableOpacity>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          Historique
        </Text>
      </View>

      {error ? (
        <View style={styles.centerContent}>
          <Text style={[styles.error, { color: theme.colors.error }]}>
            {error}
          </Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
            onPress={loadAppointmentHistory}>
            <Text style={styles.retryButtonText}>Réessayer</Text>
          </TouchableOpacity>
        </View>
      ) : loading ? (
        <View style={styles.centerContent}>
          <Text style={{ color: theme.colors.text }}>
            Chargement de l'historique...
          </Text>
        </View>
      ) : appointments.length === 0 ? (
        <View style={styles.centerContent}>
          <Text style={[styles.emptyText, { color: theme.colors.gray[600] }]}>
            Aucun rendez-vous passé
          </Text>
        </View>
      ) : (
        <ScrollView style={styles.appointmentsList}>
          {appointments.map((appointment) => (
            <View
              key={appointment.id}
              style={[
                styles.appointmentCard,
                { backgroundColor: theme.colors.surface },
              ]}>
              <View style={styles.appointmentHeader}>
                <View style={styles.professionalInfo}>
                  <Text
                    style={[styles.professionalName, { color: theme.colors.text }]}>
                    {`${appointment.professional.user.first_name} ${appointment.professional.user.last_name}`}
                  </Text>
                  <Text
                    style={[
                      styles.professionalType,
                      { color: theme.colors.gray[600] },
                    ]}>
                    {appointment.professional.type === 'doctor'
                      ? 'Médecin'
                      : 'Avocat'}{' '}
                    - {appointment.professional.speciality}
                  </Text>
                </View>
                <View
                  style={[
                    styles.statusBadge,
                    { backgroundColor: getStatusColor(appointment.status) },
                  ]}>
                  <Text style={styles.statusText}>
                    {getStatusText(appointment.status)}
                  </Text>
                </View>
              </View>

              <View style={styles.appointmentDetails}>
                <View style={styles.detailRow}>
                  <Ionicons
                    name="calendar-outline"
                    size={20}
                    color={theme.colors.gray[500]}
                  />
                  <Text
                    style={[
                      styles.detailText,
                      { color: theme.colors.gray[700] },
                    ]}>
                    {formatAppointmentDate(appointment.date_time)}
                  </Text>
                </View>

                <View style={styles.detailRow}>
                  <Ionicons
                    name={
                      appointment.type === 'video'
                        ? 'videocam-outline'
                        : 'person-outline'
                    }
                    size={20}
                    color={theme.colors.gray[500]}
                  />
                  <Text
                    style={[
                      styles.detailText,
                      { color: theme.colors.gray[700] },
                    ]}>
                    {appointment.type === 'video'
                      ? 'Consultation vidéo'
                      : 'Consultation en personne'}
                  </Text>
                </View>

                {appointment.notes && (
                  <View style={styles.notes}>
                    <Text
                      style={[styles.notesTitle, { color: theme.colors.text }]}>
                      Notes
                    </Text>
                    <Text
                      style={[
                        styles.notesText,
                        { color: theme.colors.gray[700] },
                      ]}>
                      {appointment.notes}
                    </Text>
                  </View>
                )}
              </View>
            </View>
          ))}
        </ScrollView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
  },
  backButton: {
    marginRight: 15,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  error: {
    marginBottom: 15,
    textAlign: 'center',
  },
  retryButton: {
    padding: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  appointmentsList: {
    flex: 1,
    padding: 20,
  },
  appointmentCard: {
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
  },
  appointmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  professionalInfo: {
    flex: 1,
  },
  professionalName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  professionalType: {
    fontSize: 14,
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  appointmentDetails: {
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  detailText: {
    fontSize: 14,
  },
  notes: {
    marginTop: 10,
    padding: 10,
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
  },
  notesTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  notesText: {
    fontSize: 14,
    lineHeight: 20,
  },
});